import express from "express";
import dotenv from "dotenv";
import { generateSignedUrl } from "./cloudfrontSigner.js";

dotenv.config();
const app = express();


app.post("/upload", upload.single("file"), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: "No file uploaded" });
        }

        const key = await uploadFileToS3(req.file);

        res.json({
            message: "File uploaded successfully",
            key,
            s3Url: `https://${process.env.S3_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: "Upload failed" });
    }
});

// Example endpoint: get signed URL for a file
app.get("/get-signed-url", (req, res) => {
    const { file } = req.query; // e.g., ?file=/mydocs/file.pdf

    if (!file) {
        return res.status(400).json({ error: "file query param required" });
    }

    try {
        const signedUrl = generateSignedUrl(file, 3600); // 1 hour expiry
        res.json({ signedUrl });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: "Failed to generate signed URL" });
    }
});

app.listen(3000, () => {
    console.log("Server running on http://localhost:3529");
});
