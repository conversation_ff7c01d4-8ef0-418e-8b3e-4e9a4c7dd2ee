import express from "express";
import dotenv from "dotenv";
import multer from "multer";
import { generateSignedUrl } from "./cloudfrontSigner.js";
import { uploadFileToS3 } from "./s3Upload.js";

dotenv.config();
const app = express();

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Configure multer for file uploads
const upload = multer({ dest: "uploads/" });

// Health check endpoint
app.get("/", (req, res) => {
    res.json({
        message: "CloudFront S3 Upload Service is running",
        endpoints: {
            upload: "POST /upload",
            signedUrl: "GET /get-signed-url?file=<filepath>"
        }
    });
});

app.post("/upload", upload.single("file"), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: "No file uploaded" });
        }

        const key = await uploadFileToS3(req.file);

        res.json({
            message: "File uploaded successfully",
            key,
            s3Url: `https://${process.env.S3_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: "Upload failed" });
    }
});

// Example endpoint: get signed URL for a file
app.get("/get-signed-url", (req, res) => {
    const { file } = req.query; // e.g., ?file=/mydocs/file.pdf

    if (!file) {
        return res.status(400).json({ error: "file query param required" });
    }

    try {
        const signedUrl = generateSignedUrl(file, 60); // 1 minute expiry
        res.json({ signedUrl });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: "Failed to generate signed URL" });
    }
});

app.listen(3000, () => {
    console.log("Server running on http://localhost:3000");
});
