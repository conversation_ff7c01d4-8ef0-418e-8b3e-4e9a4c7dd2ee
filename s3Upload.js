import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import fs from "fs";
import path from "path";
import dotenv from "dotenv";
dotenv.config({path: path.join(__dirname, ".env")});

const s3 = new S3Client({
    region: process.env.AWS_REGION, // e.g., "us-east-1"
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
});

/**
 * Upload file to S3
 * @param {Object} file - Multer file object
 * @returns {string} S3 file key
 */
export async function uploadFileToS3(file) {
    const fileStream = fs.createReadStream(file.path);

    const uploadParams = {
        Bucket: process.env.S3_BUCKET_NAME,
        Key: file.originalname, // you can also prefix with folder e.g., `uploads/${file.originalname}`
        Body: fileStream,
        ContentType: file.mimetype,
    };

    await s3.send(new PutObjectCommand(uploadParams));

    return file.originalname;
}
