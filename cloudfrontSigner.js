import { getSignedUrl } from "@aws-sdk/cloudfront-signer";
import fs from "fs";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Load your private key
const privateKey = fs.readFileSync(process.env.CLOUDFRONT_PRIVATE_KEY_PATH, "utf8");

/**
 * Generate a signed CloudFront URL
 * @param {string} filePath - The path to the file (e.g., "/mydocs/file.pdf")
 * @param {number} expiresIn - Expiration time in seconds (default: 1 hour)
 * @returns {string} Signed URL
 */
export function generateSignedUrl(filePath, expiresIn = 3600) {
    const url = `https://${process.env.CLOUDFRONT_DOMAIN}/${filePath}`;

    const signedUrl = getSignedUrl({
        url,
        keyPairId: process.env.CLOUDFRONT_PUBLIC_KEY_ID,
        privateKey,
        dateLessThan: new Date(Date.now() + expiresIn * 1000).toISOString(),
    });

    return signedUrl;
}
